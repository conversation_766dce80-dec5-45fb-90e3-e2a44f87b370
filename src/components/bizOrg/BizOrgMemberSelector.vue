<template>
  <n-modal
    :show="modelVisible"
    @update:show="updateVisible"
    :title="title || '选择业务机构成员'"
    preset="card"
    :style="{ width: '1200px', maxWidth: '95%', height: '80vh' }"
    :mask-closable="false"
    :auto-focus="false"
    class="biz-org-member-selector-modal"
  >
    <div class="biz-org-member-selector-content">
      <!-- 左右布局容器 -->
      <div class="layout-container">
        <!-- 左侧：机构列表 -->
        <div class="left-panel">
          <biz-org-list-selector
            ref="orgSelectorRef"
            @select-org="handleOrgSelect"
          />
        </div>

        <!-- 右侧：成员列表 -->
        <div class="right-panel">
          <n-card title="机构成员" class="members-card">
            <template #header-extra>
              <n-space>
                <n-text v-if="currentOrgName" type="info">
                  {{ currentOrgName }}
                </n-text>
                <n-button @click="refreshMembers" secondary size="small">
                  <template #icon>
                    <n-icon>
                      <RefreshOutline />
                    </n-icon>
                  </template>
                  刷新
                </n-button>
              </n-space>
            </template>

            <!-- 搜索框 -->
            <div class="search-area">
              <n-input
                v-model:value="searchKeyword"
                placeholder="请输入成员姓名或员工ID搜索"
                clearable
                @keydown.enter="handleSearch"
                @clear="handleSearch"
              >
                <template #prefix>
                  <n-icon>
                    <SearchOutline />
                  </n-icon>
                </template>
              </n-input>
            </div>

            <!-- 已选择成员显示 -->
            <div
              v-if="mode === 'multiple' && selectedMembers.length > 0"
              class="selected-members-area"
            >
              <n-text strong
                >已选择 {{ selectedMembers.length }} 个成员：</n-text
              >
              <div class="selected-tags">
                <n-tag
                  v-for="member in selectedMembers"
                  :key="member.id"
                  type="success"
                  closable
                  @close="removeMember(member)"
                >
                  {{ member.agentName }} ({{ member.agentId }})
                </n-tag>
              </div>
            </div>

            <!-- 成员列表 -->
            <div class="members-list-container">
              <n-scrollbar style="max-height: calc(60vh - 200px)">
                <n-list hoverable clickable>
                  <n-list-item
                    v-for="member in filteredMembersList"
                    :key="member.id"
                    :class="{
                      selected: isSelected(member),
                      disabled: isDisabled(member),
                    }"
                    @click="handleMemberSelect(member)"
                    @dblclick="handleMemberDoubleClick(member)"
                  >
                    <div class="member-item">
                      <div class="member-info">
                        <div class="member-name">{{ member.agentName }}</div>
                        <div class="member-details">
                          <n-text depth="3"
                            >员工ID: {{ member.agentId }}</n-text
                          >
                          <n-text
                            v-if="member.businessRole"
                            depth="3"
                            class="role-text"
                          >
                            角色: {{ getRoleDisplayName(member.businessRole) }}
                          </n-text>
                        </div>
                      </div>
                      <div class="member-actions">
                        <n-checkbox
                          v-if="mode === 'multiple'"
                          :checked="isSelected(member)"
                          :disabled="isDisabled(member)"
                          @click.stop
                          @update:checked="
                            (checked) => handleCheckboxChange(member, checked)
                          "
                        />
                        <n-icon v-else-if="isSelected(member)" color="#18a058">
                          <CheckmarkCircleOutline />
                        </n-icon>
                      </div>
                    </div>
                  </n-list-item>
                </n-list>
              </n-scrollbar>
            </div>

            <!-- 加载状态 -->
            <div v-if="membersLoading" class="loading-container">
              <n-spin size="medium" />
            </div>

            <!-- 空状态 -->
            <div
              v-if="!membersLoading && !currentOrgId"
              class="empty-container"
            >
              <n-empty description="请先选择机构" />
            </div>

            <div
              v-if="
                !membersLoading &&
                currentOrgId &&
                filteredMembersList.length === 0
              "
              class="empty-container"
            >
              <n-empty description="该机构暂无成员" />
            </div>
          </n-card>
        </div>
      </div>
    </div>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :disabled="selectedMembers.length === 0"
          @click="handleConfirm"
        >
          确定
          {{ selectedMembers.length > 0 ? `(${selectedMembers.length})` : "" }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import {
  NModal,
  NCard,
  NInput,
  NButton,
  NIcon,
  NSpace,
  NList,
  NListItem,
  NTag,
  NText,
  NCheckbox,
  NScrollbar,
  NSpin,
  NEmpty,
} from "naive-ui";
import {
  SearchOutline,
  RefreshOutline,
  CheckmarkCircleOutline,
} from "@vicons/ionicons5";
import BizOrgListSelector from "./BizOrgListSelector.vue";
import { getBizOrgMembersList } from "@/api/bizOrgMembers";
import { getRoleNameById } from "@/utils/roleCache";
import messages from "@/utils/messages";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: "single", // 'single' | 'multiple'
    validator: (value) => ["single", "multiple"].includes(value),
  },
  initialOrgId: {
    type: [Number, String],
    default: null,
  },
  title: {
    type: String,
    default: "选择业务机构成员",
  },
});

// 定义组件事件
const emit = defineEmits(["select", "cancel", "update:visible"]);

// 组件状态
const orgSelectorRef = ref(null);
const membersLoading = ref(false);
const searchKeyword = ref("");
const membersList = ref([]);
const selectedMembers = ref([]);
const currentOrgId = ref(null);
const currentOrgName = ref("");

// 计算属性：控制弹窗显示
const modelVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

// 计算属性：过滤后的成员列表
const filteredMembersList = computed(() => {
  if (!searchKeyword.value) {
    return membersList.value;
  }

  const keyword = searchKeyword.value.toLowerCase();
  return membersList.value.filter(
    (member) =>
      member.agentName.toLowerCase().includes(keyword) ||
      member.agentId.toLowerCase().includes(keyword)
  );
});

// 更新弹窗显示状态
const updateVisible = (value) => {
  emit("update:visible", value);
};

// 处理机构选择
const handleOrgSelect = async (orgId, orgInfo) => {
  currentOrgId.value = orgId;
  currentOrgName.value = orgInfo?.orgName || "";

  // 清空之前的搜索和选择
  searchKeyword.value = "";

  // 获取该机构的成员列表
  await fetchMembersList(orgId);
};

// 获取成员列表
const fetchMembersList = async (orgId) => {
  if (!orgId) return;

  membersLoading.value = true;
  try {
    const response = await getBizOrgMembersList(orgId, {
      keywords: searchKeyword.value,
    });

    if (response.code === 200) {
      membersList.value = response.data || [];
    } else {
      messages.error(response.message || "获取成员列表失败");
      membersList.value = [];
    }
  } catch (error) {
    console.error("获取成员列表失败:", error);
    messages.error("获取成员列表失败，请稍后重试");
    membersList.value = [];
  } finally {
    membersLoading.value = false;
  }
};

// 刷新成员列表
const refreshMembers = () => {
  if (currentOrgId.value) {
    fetchMembersList(currentOrgId.value);
  }
};

// 处理搜索
const handleSearch = () => {
  // 搜索是通过计算属性实现的，这里可以添加额外的搜索逻辑
  if (currentOrgId.value) {
    fetchMembersList(currentOrgId.value);
  }
};

// 获取角色显示名称
const getRoleDisplayName = (roleId) => {
  return getRoleNameById(roleId) || roleId || "未设置";
};

// 判断成员是否已选中
const isSelected = (member) => {
  return selectedMembers.value.some((selected) => selected.id === member.id);
};

// 判断成员是否禁用
const isDisabled = (member) => {
  // 可以根据业务需求添加禁用逻辑
  return false;
};

// 处理成员选择
const handleMemberSelect = (member) => {
  if (isDisabled(member)) return;

  if (props.mode === "single") {
    selectedMembers.value = [formatMemberData(member)];
  } else {
    if (isSelected(member)) {
      removeMember(member);
    } else {
      selectedMembers.value.push(formatMemberData(member));
    }
  }
};

// 处理成员双击
const handleMemberDoubleClick = (member) => {
  if (isDisabled(member)) return;

  if (props.mode === "single") {
    selectedMembers.value = [formatMemberData(member)];
    handleConfirm();
  }
};

// 处理复选框变化
const handleCheckboxChange = (member, checked) => {
  if (checked) {
    if (!isSelected(member)) {
      selectedMembers.value.push(formatMemberData(member));
    }
  } else {
    removeMember(member);
  }
};

// 移除成员
const removeMember = (member) => {
  selectedMembers.value = selectedMembers.value.filter(
    (selected) => selected.id !== member.id
  );
};

// 格式化成员数据
const formatMemberData = (member) => {
  return {
    id: member.id,
    name: member.agentName,
    agentId: member.agentId,
    agentName: member.agentName,
    biz_org_id: member.orgId || currentOrgId.value,
    businessRole: member.businessRole,
    dataPermissions: member.dataPermissions,
    dataRange: member.dataRange,
    dataRangeNames: member.dataRangeNames,
  };
};

// 处理确认
const handleConfirm = () => {
  const result =
    props.mode === "single"
      ? selectedMembers.value[0] || null
      : selectedMembers.value;

  emit("select", result);
  emit("update:visible", false);
};

// 处理取消
const handleCancel = () => {
  emit("cancel");
  emit("update:visible", false);
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 重置状态
      selectedMembers.value = [];
      searchKeyword.value = "";

      // 如果有初始机构ID，则选中该机构
      if (props.initialOrgId) {
        // 等待组件渲染完成后设置初始机构
        setTimeout(() => {
          handleOrgSelect(props.initialOrgId, { orgName: "指定机构" });
        }, 100);
      }
    }
  }
);
</script>

<style lang="scss" scoped>
.biz-org-member-selector-modal {
  :deep(.n-card__content) {
    padding: 0;
    height: 100%;
  }
}

.biz-org-member-selector-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.layout-container {
  display: flex;
  height: calc(80vh - 120px);
  gap: 16px;
}

.left-panel {
  width: 400px;
  flex-shrink: 0;
  border-right: 1px solid var(--n-border-color);
  padding-right: 16px;
}

.right-panel {
  flex: 1;
  min-width: 0;

  .members-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.n-card__content) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 16px;
    }
  }
}

.search-area {
  margin-bottom: 16px;
}

.selected-members-area {
  margin-bottom: 16px;
  padding: 12px;
  background-color: var(--n-color-target);
  border-radius: 6px;
  border: 1px solid var(--n-border-color);

  .selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
  }
}

.members-list-container {
  flex: 1;
  min-height: 0;
}

.member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 0;

  .member-info {
    flex: 1;
    min-width: 0;

    .member-name {
      font-weight: 500;
      color: var(--n-text-color);
      margin-bottom: 4px;
    }

    .member-details {
      display: flex;
      gap: 16px;

      .role-text {
        color: var(--n-text-color-2);
      }
    }
  }

  .member-actions {
    flex-shrink: 0;
    margin-left: 12px;
  }
}

:deep(.n-list-item) {
  &.selected {
    background-color: rgba(24, 160, 88, 0.1);
    border-left: 3px solid #18a058;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      background-color: transparent;
    }
  }

  &:hover:not(.disabled) {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

// 响应式设计
@media (max-width: 1024px) {
  .layout-container {
    flex-direction: column;
    height: auto;
  }

  .left-panel {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--n-border-color);
    padding-right: 0;
    padding-bottom: 16px;
    margin-bottom: 16px;
  }

  .right-panel {
    width: 100%;
  }
}
</style>
